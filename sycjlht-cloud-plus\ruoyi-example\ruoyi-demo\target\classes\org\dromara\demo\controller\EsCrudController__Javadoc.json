{"doc": " 搜索引擎 crud 演示案例\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "select", "paramTypes": ["java.lang.String"], "doc": " 查询(指定)\n\n @param title 标题\n"}, {"name": "search", "paramTypes": ["java.lang.String"], "doc": " 搜索(模糊)\n\n @param key 搜索关键字\n"}, {"name": "insert", "paramTypes": ["org.dromara.demo.domain.Document"], "doc": " 插入\n"}, {"name": "update", "paramTypes": ["org.dromara.demo.domain.Document"], "doc": " 更新\n"}, {"name": "delete", "paramTypes": ["java.lang.String"], "doc": " 删除\n\n @param id 主键\n"}], "constructors": []}