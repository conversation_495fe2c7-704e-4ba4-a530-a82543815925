{"doc": " 测试单表Controller\n\n <AUTHOR>\n @date 2021-07-26\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询测试单表列表\n"}, {"name": "page", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 自定义分页查询\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 导入测试-校验\n\n @param file 导入文件\n"}, {"name": "export", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出测试单表列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取测试单表详细信息\n\n @param id 测试ID\n"}, {"name": "add", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": " 新增测试单表\n"}, {"name": "edit", "paramTypes": ["org.dromara.demo.domain.bo.TestDemoBo"], "doc": " 修改测试单表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除测试单表\n\n @param ids 测试ID串\n"}], "constructors": []}